import { getTidalStreamingUrl } from '../Api/TidalAPI';
import { ToastAndroid } from 'react-native';

/**
 * TidalMusicHandler - Handles Tidal-specific music operations
 * This file contains functions to handle Tidal music playback,
 * format conversion, and streaming URL generation.
 */

/**
 * Convert Tidal song data to app format for music player
 * @param {Object} tidalSong - Song data from Tidal API
 * @returns {Promise<Object>} Formatted song object for music player
 */
async function formatTidalSongForPlayer(tidalSong) {
  try {
    // Get streaming URL from Tidal
    const streamingUrl = await getTidalStreamingUrl(tidalSong.tidalUrl, 'LOSSLESS');
    
    return {
      url: streamingUrl,
      title: tidalSong.title || tidalSong.name,
      artist: tidalSong.artist || 'Unknown Artist',
      artwork: tidalSong.image?.[2]?.url || tidalSong.image?.[0]?.url || null,
      image: tidalSong.image?.[2]?.url || tidalSong.image?.[0]?.url || null,
      duration: tidalSong.duration || 0,
      id: tidalSong.id,
      language: tidalSong.language || 'en',
      artistID: tidalSong.primary_artists_id || '',
      source: 'tidal',
      sourceType: 'tidal'
    };
  } catch (error) {
    console.error('Error formatting Tidal song for player:', error);
    ToastAndroid.show('Failed to get Tidal streaming URL', ToastAndroid.SHORT);
    throw error;
  }
}

/**
 * Convert multiple Tidal songs for playlist
 * @param {Array} tidalSongs - Array of Tidal song objects
 * @returns {Promise<Array>} Array of formatted song objects
 */
async function formatTidalSongsForPlaylist(tidalSongs) {
  try {
    const formattedSongs = [];
    
    // Process songs in batches to avoid overwhelming the API
    const batchSize = 3;
    for (let i = 0; i < tidalSongs.length; i += batchSize) {
      const batch = tidalSongs.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (song) => {
        try {
          return await formatTidalSongForPlayer(song);
        } catch (error) {
          console.error(`Failed to format song ${song.title}:`, error);
          return null; // Skip failed songs
        }
      });
      
      const batchResults = await Promise.all(batchPromises);
      formattedSongs.push(...batchResults.filter(song => song !== null));
      
      // Small delay between batches to be respectful to the API
      if (i + batchSize < tidalSongs.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return formattedSongs;
  } catch (error) {
    console.error('Error formatting Tidal songs for playlist:', error);
    throw error;
  }
}

/**
 * Check if a song is from Tidal source
 * @param {Object} song - Song object
 * @returns {boolean} True if song is from Tidal
 */
function isTidalSong(song) {
  return song?.source === 'tidal' || song?.sourceType === 'tidal';
}

/**
 * Get quality options for Tidal
 * @returns {Array} Available quality options
 */
function getTidalQualityOptions() {
  return [
    { value: 'LOW', label: '96 kbps' },
    { value: 'HIGH', label: '320 kbps' },
    { value: 'LOSSLESS', label: 'FLAC (Lossless)' }
  ];
}

/**
 * Handle Tidal-specific download operations
 * Note: This is a placeholder for future implementation
 * @param {Object} song - Tidal song object
 * @returns {Promise<boolean>} Success status
 */
async function downloadTidalSong(song) {
  try {
    // For now, show message that download is not implemented
    ToastAndroid.show('Tidal downloads will be implemented in future updates', ToastAndroid.LONG);
    return false;
  } catch (error) {
    console.error('Error downloading Tidal song:', error);
    return false;
  }
}

/**
 * Show unsupported feature message for Tidal
 * @param {string} feature - Feature name (e.g., 'playlists', 'albums')
 */
function showTidalUnsupportedMessage(feature) {
  ToastAndroid.show(
    `${feature} are not supported with Tidal. Please use Saavn for ${feature}.`,
    ToastAndroid.LONG
  );
}

/**
 * Get appropriate error message for Tidal operations
 * @param {Error} error - Error object
 * @returns {string} User-friendly error message
 */
function getTidalErrorMessage(error) {
  if (error.message?.includes('timeout')) {
    return 'Tidal service is taking too long to respond. Please try again.';
  } else if (error.message?.includes('network')) {
    return 'Network error while connecting to Tidal. Check your internet connection.';
  } else if (error.message?.includes('streaming URL')) {
    return 'Unable to get streaming URL from Tidal. The track may not be available.';
  } else {
    return 'An error occurred with Tidal service. Please try again or use Saavn.';
  }
}

export {
  formatTidalSongForPlayer,
  formatTidalSongsForPlaylist,
  isTidalSong,
  getTidalQualityOptions,
  downloadTidalSong,
  showTidalUnsupportedMessage,
  getTidalErrorMessage
};
