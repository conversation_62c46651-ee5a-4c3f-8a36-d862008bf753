import axios from "axios";
import { getCachedData, CACHE_GROUPS } from './CacheManager';

const TIDAL_BASE_URL = 'https://api.paxsenix.biz.id';

/**
 * Search for songs on Tidal
 * @param {string} searchText - Search query
 * @param {number} page - Page number (not used by Tidal API but kept for consistency)
 * @param {number} limit - Limit (not used by Tidal API but kept for consistency)
 * @returns {Promise<Object>} Search results
 */
async function getTidalSearchSongData(searchText, page = 1, limit = 20) {
  const cacheKey = `tidal_search_${searchText}_page${page}_limit${limit}`;
  
  const fetchFunction = async () => {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${TIDAL_BASE_URL}/tidal/search?q=${encodeURIComponent(searchText)}`,
      headers: {},
      timeout: 10000
    };
    
    try {
      const response = await axios.request(config);
      
      // Transform Tidal response to match app format
      if (response.data && response.data.tracks) {
        return {
          success: true,
          data: {
            results: response.data.tracks.map(track => ({
              id: track.id,
              name: track.title,
              title: track.title,
              artist: track.artist?.name || 'Unknown Artist',
              artists: {
                primary: [{ name: track.artist?.name || 'Unknown Artist' }]
              },
              primary_artists_id: track.artist?.id || '',
              duration: track.duration || 0,
              image: track.album?.cover ? [
                { url: track.album.cover },
                { url: track.album.cover },
                { url: track.album.cover }
              ] : [],
              downloadUrl: [], // Will be populated when playing
              language: 'en', // Default for Tidal
              source: 'tidal',
              tidalUrl: track.url // Store original Tidal URL for streaming
            }))
          }
        };
      }
      
      return {
        success: false,
        message: 'No tracks found'
      };
    } catch (error) {
      console.error('Tidal search error:', error);
      return {
        success: false,
        error: error.message || 'Network error',
        message: 'Failed to search Tidal'
      };
    }
  };

  try {
    return await getCachedData(cacheKey, fetchFunction, 30, CACHE_GROUPS.SEARCH);
  } catch (error) {
    console.error(`Error getting Tidal search data for "${searchText}":`, error);
    return {
      success: false,
      error: error.message,
      message: 'Search failed'
    };
  }
}

/**
 * Get Tidal track details
 * @param {string} trackId - Tidal track ID
 * @returns {Promise<Object>} Track details
 */
async function getTidalTrackData(trackId) {
  const cacheKey = `tidal_track_${trackId}`;
  
  const fetchFunction = async () => {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${TIDAL_BASE_URL}/tidal/track?id=${trackId}`,
      headers: {},
      timeout: 10000
    };
    
    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(`Tidal track error for ID ${trackId}:`, error);
      throw error;
    }
  };

  try {
    return await getCachedData(cacheKey, fetchFunction, 60, CACHE_GROUPS.SONGS);
  } catch (error) {
    console.error(`Error getting Tidal track data for ID ${trackId}:`, error);
    throw error;
  }
}

/**
 * Get Tidal streaming URL
 * @param {string} tidalUrl - Original Tidal track URL
 * @param {string} quality - Quality preference (default: LOSSLESS)
 * @returns {Promise<string>} Streaming URL
 */
async function getTidalStreamingUrl(tidalUrl, quality = 'LOSSLESS') {
  try {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `${TIDAL_BASE_URL}/dl/tidal?url=${encodeURIComponent(tidalUrl)}&quality=${quality}`,
      headers: {},
      timeout: 15000
    };
    
    const response = await axios.request(config);
    
    if (response.data && response.data.downloadUrl) {
      return response.data.downloadUrl;
    }
    
    throw new Error('No streaming URL found');
  } catch (error) {
    console.error('Error getting Tidal streaming URL:', error);
    throw error;
  }
}

/**
 * Get Tidal album data (placeholder - shows message that albums not supported)
 * @param {string} albumId - Album ID
 * @returns {Promise<Object>} Error message
 */
async function getTidalAlbumData(albumId) {
  return {
    success: false,
    message: 'Albums are not supported with Tidal. Please use Saavn for albums.',
    unsupported: true
  };
}

/**
 * Get Tidal playlist data (placeholder - shows message that playlists not supported)
 * @param {string} playlistId - Playlist ID
 * @returns {Promise<Object>} Error message
 */
async function getTidalPlaylistData(playlistId) {
  return {
    success: false,
    message: 'Playlists are not supported with Tidal. Please use Saavn for playlists.',
    unsupported: true
  };
}

export {
  getTidalSearchSongData,
  getTidalTrackData,
  getTidalStreamingUrl,
  getTidalAlbumData,
  getTidalPlaylistData
};
