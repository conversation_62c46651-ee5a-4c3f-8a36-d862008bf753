import { MainWrapper } from "../Layout/MainWrapper";
import { SearchBar } from "../Component/Global/SearchBar";
import Tabs from "../Component/Global/Tabs/Tabs";
import { useEffect, useState } from "react";
import { getSearchSongData } from "../Api/Songs";
import { View, TouchableOpacity, ToastAndroid } from "react-native";
import SongDisplay from "../Component/SearchPage/SongDisplay";
import { LoadingComponent } from "../Component/Global/Loading";
import { getSearchPlaylistData } from "../Api/Playlist";
import PlaylistDisplay from "../Component/SearchPage/PlaylistDisplay";
import { getSearchAlbumData } from "../Api/Album";
import AlbumsDisplay from "../Component/SearchPage/AlbumDisplay";
import { Spacer } from "../Component/Global/Spacer";
import { getTidalSearchSongData } from "../Api/TidalAPI";
import { GetTidalEnabled } from "../LocalStorage/AppSettings";
import { PlainText } from "../Component/Global/PlainText";
import { useTheme } from "@react-navigation/native";

export const SearchPage = ({navigation}) => {
  const { colors } = useTheme();
  const [ActiveTab, setActiveTab] = useState(0)
  const [query, setQuery] = useState("");
  // const [ApiQuery, setApiQuery] = useState("");
  const [SearchText, setSearchText] = useState("")
  const [Loading, setLoading] = useState(false)
  const [Data, setData] = useState({});
  const [tidalEnabled, setTidalEnabled] = useState(false);
  const [selectedSource, setSelectedSource] = useState('saavn'); // 'saavn' or 'tidal'
  const limit = 20
  async function fetchSearchData(text){
    if (SearchText !== ""){
      try {
        setLoading(true)
        let data
        if (ActiveTab === 0){
          // Songs tab - check source
          if (selectedSource === 'tidal' && tidalEnabled) {
            data = await getTidalSearchSongData(text,1,limit)
          } else {
            data = await getSearchSongData(text,1,limit)
          }
        } else if (ActiveTab === 1){
          // Playlists - only Saavn supported
          if (selectedSource === 'tidal' && tidalEnabled) {
            ToastAndroid.show('Playlists are not supported with Tidal. Showing Saavn results.', ToastAndroid.LONG);
          }
          data = await getSearchPlaylistData(text,1,limit)
        }
        else if (ActiveTab === 2){
          // Albums - only Saavn supported
          if (selectedSource === 'tidal' && tidalEnabled) {
            ToastAndroid.show('Albums are not supported with Tidal. Showing Saavn results.', ToastAndroid.LONG);
          }
          data = await getSearchAlbumData(text,1,limit)
        }
        setData(data)
      } catch (e) {
        console.log(e);
      } finally {
        setLoading(false)
      }
    } else {
      setData([])
    }
  }
  useEffect(() => {
    if (SearchText){
      fetchSearchData(SearchText)
    } else {
      setData([])
    }
  }, [SearchText]);
  useEffect(() => {
    const timeoutId = setTimeout(()=>setSearchText(query), 350)
    return () => {
      clearTimeout(timeoutId)
    }
  }, [query]);
  useEffect(()=>{
      fetchSearchData(SearchText)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[ActiveTab, selectedSource])

  // Load Tidal preference on mount
  useEffect(() => {
    const loadTidalPreference = async () => {
      const enabled = await GetTidalEnabled();
      setTidalEnabled(enabled);
    };
    loadTidalPreference();
  }, []);
  return (
    <MainWrapper>
      <Spacer/>
      <SearchBar navigation={navigation} onChange={(text)=>{
        setQuery(text)
      }}/>
      <Tabs tabs={["Songs","Playlists","Albums"]} setState={setActiveTab} state={ActiveTab}/>

      {/* Source Switcher - Only show for Songs tab and when Tidal is enabled */}
      {ActiveTab === 0 && tidalEnabled && (
        <View style={{
          flexDirection: 'row',
          justifyContent: 'center',
          marginVertical: 10,
          paddingHorizontal: 10,
        }}>
          <View style={{
            flexDirection: 'row',
            backgroundColor: colors.card,
            borderRadius: 25,
            padding: 3,
          }}>
            <TouchableOpacity
              style={{
                paddingHorizontal: 20,
                paddingVertical: 8,
                borderRadius: 20,
                backgroundColor: selectedSource === 'saavn' ? colors.primary : 'transparent',
              }}
              onPress={() => setSelectedSource('saavn')}
            >
              <PlainText
                text="Saavn"
                style={{
                  color: selectedSource === 'saavn' ? 'white' : colors.text,
                  fontWeight: selectedSource === 'saavn' ? 'bold' : 'normal'
                }}
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                paddingHorizontal: 20,
                paddingVertical: 8,
                borderRadius: 20,
                backgroundColor: selectedSource === 'tidal' ? colors.primary : 'transparent',
              }}
              onPress={() => setSelectedSource('tidal')}
            >
              <PlainText
                text="Tidal"
                style={{
                  color: selectedSource === 'tidal' ? 'white' : colors.text,
                  fontWeight: selectedSource === 'tidal' ? 'bold' : 'normal'
                }}
              />
            </TouchableOpacity>
          </View>
        </View>
      )}

      <Spacer height={15}/>
      {Loading && <LoadingComponent loading={Loading}/>}
      {!Loading && <View style={{
        paddingHorizontal:10,
      }}>
          {ActiveTab === 0 && !Loading && <SongDisplay data={Data} limit={limit} Searchtext={SearchText} source={selectedSource}/>}
          {ActiveTab === 1 && !Loading && <PlaylistDisplay data={Data} limit={limit} Searchtext={SearchText}/>}
          {ActiveTab === 2 && !Loading && <AlbumsDisplay data={Data} limit={limit} Searchtext={SearchText}/>}
      </View>}
    </MainWrapper>
  );
};
